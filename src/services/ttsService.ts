// 语音合成服务 - SiliconFlow API 封装
import { VoiceListService, type FormattedVoice } from './voiceListService';
import { ActivityService } from './activityService';
import { VoiceService } from './voiceService';

export interface TTSRequest {
  model: string;
  input: string;
  voice: string;
  speed?: number;        // [0.25, 4.0]，默认1.0
  pitch?: number;        // 音调控制
  emotion?: string;      // 情感控制（暂时保留但不在UI中使用）
  gain?: number;         // 音频增益 [-10, 10] dB，默认0.0
  response_format?: string; // 输出格式，固定为'wav'
  sample_rate?: number;  // 采样率，固定为44100
}

export interface TTSResponse {
  success: boolean;
  audioUrl?: string;
  audioData?: ArrayBuffer;
  message?: string;
  error?: string;
  usage?: {
    characters: number;
    cost: number;
  };
}

export interface TTSError {
  code: string;
  message: string;
  details?: any;
}

export interface TTSUsageStats {
  totalRequests: number;
  totalCharacters: number;
  totalCost: number;
  successRate: number;
}

export class TTSService {
  private static readonly API_URL = 'https://api.siliconflow.cn/v1/audio/speech';
  private static readonly API_KEY = 'sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw';
  private static readonly DEFAULT_MODEL = 'FunAudioLLM/CosyVoice2-0.5B';

  // 音色缓存
  private static voiceCache: Map<string, Array<{ id: string; name: string; preview: string; isCustom?: boolean; isCloned?: boolean; gender?: string }>> = new Map();
  private static cacheTimestamp: Map<string, number> = new Map();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  // 支持的语音列表（向后兼容）
  private static readonly SUPPORTED_VOICES = {
    'zh-CN-XiaoxiaoNeural': 'speech:xzzc01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    'zh-CN-YunxiNeural': 'speech:yunxi01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    'zh-CN-XiaoyiNeural': 'speech:xiaoyi01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    'zh-CN-YunjianNeural': 'speech:yunjian01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
  };

  /**
   * 生成语音
   * @param request 语音合成请求参数
   * @param userId 用户ID（用于记录活动）
   * @returns Promise<TTSResponse>
   */
  static async generateSpeech(request: TTSRequest, userId?: string): Promise<TTSResponse> {
    const startTime = Date.now();

    try {
      // 参数验证
      const validationError = this.validateRequest(request);
      if (validationError) {
        this.logError('VALIDATION_ERROR', validationError.message, { request });
        return {
          success: false,
          error: validationError.message,
        };
      }

      // 转换语音 ID
      const voiceId = await this.mapVoiceId(request.voice, userId);

      // 构建请求体
      const requestBody = {
        model: request.model || this.DEFAULT_MODEL,
        input: request.input,
        voice: voiceId,
        // 添加可选参数支持
        ...(request.speed !== undefined && { speed: request.speed }),
        ...(request.pitch !== undefined && { pitch: request.pitch }),
        ...(request.gain !== undefined && { gain: request.gain }),
        // 固定参数
        response_format: 'wav',
        sample_rate: 44100,
      };

      this.logInfo('TTS Request Started', {
        characters: request.input.length,
        voice: request.voice,
        model: requestBody.model
      });

      // 发送请求
      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
          'Content-Type': 'application/json',
          'User-Agent': 'SoulVoice/1.0.0',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        const duration = Date.now() - startTime;

        this.logError('API_ERROR', `HTTP ${response.status}`, {
          status: response.status,
          errorText,
          duration,
          request: requestBody
        });

        const errorMessage = `API 请求失败: ${response.status} - ${this.getErrorMessage(response.status)}`;

        // 记录失败的活动
        if (userId) {
          try {
            await ActivityService.logApiCall(userId, 'tts', 'error', {
              characters: request.input.length,
              errorMessage,
              responseTime: duration,
            });
          } catch (error) {
            console.error('Failed to log activity:', error);
          }
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        // JSON 响应，可能包含错误信息
        const jsonData = await response.json();
        if (jsonData.error) {
          return {
            success: false,
            error: jsonData.error.message || '语音生成失败',
          };
        }
        
        // 如果 JSON 响应包含音频 URL
        if (jsonData.url || jsonData.audio_url) {
          return {
            success: true,
            audioUrl: jsonData.url || jsonData.audio_url,
            usage: {
              characters: request.input.length,
              cost: this.calculateCost(request.input.length),
            },
          };
        }
      }
      
      if (contentType?.includes('audio/')) {
        // 直接返回音频数据
        const audioData = await response.arrayBuffer();
        const duration = Date.now() - startTime;

        this.logInfo('TTS Request Successful', {
          characters: request.input.length,
          audioSize: audioData.byteLength,
          duration,
          cost: this.calculateCost(request.input.length)
        });

        // 记录成功的活动
        if (userId) {
          try {
            await ActivityService.logApiCall(userId, 'tts', 'success', {
              characters: request.input.length,
              cost: this.calculateCost(request.input.length),
              responseTime: duration,
            });
          } catch (error) {
            console.error('Failed to log activity:', error);
          }
        }

        return {
          success: true,
          audioData,
          usage: {
            characters: request.input.length,
            cost: this.calculateCost(request.input.length),
          },
        };
      }

      const duration = Date.now() - startTime;
      this.logError('UNKNOWN_RESPONSE_FORMAT', '未知的响应格式', {
        contentType,
        duration,
        request: requestBody
      });

      return {
        success: false,
        error: '未知的响应格式',
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logError('NETWORK_ERROR', error instanceof Error ? error.message : '网络请求失败', {
        duration,
        request: request
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '网络请求失败',
      };
    }
  }

  /**
   * 验证请求参数
   */
  private static validateRequest(request: TTSRequest): TTSError | null {
    if (!request.input || request.input.trim().length === 0) {
      return {
        code: 'INVALID_INPUT',
        message: '输入文本不能为空',
      };
    }

    if (request.input.length > 5000) {
      return {
        code: 'INPUT_TOO_LONG',
        message: '输入文本长度不能超过 5000 字符',
      };
    }

    if (!request.voice) {
      return {
        code: 'INVALID_VOICE',
        message: '必须指定语音类型',
      };
    }

    return null;
  }

  /**
   * 映射语音 ID
   */
  private static async mapVoiceId(voiceId: string, userId?: string): Promise<string> {
    // 如果是自定义语音（克隆的语音），直接返回
    if (voiceId.startsWith('speech:')) {
      return voiceId;
    }

    // 首先尝试从新的音色管理系统中查找
    try {
      const voice = await VoiceService.findVoiceById(voiceId, userId);
      if (voice) {
        return voice.uri;
      }
    } catch (error) {
      this.logError('VOICE_MAPPING_ERROR', '从音色管理系统获取音色失败', { error });
    }

    // 回退到旧的音色列表服务
    try {
      const allVoices = await VoiceListService.getAllVoices();
      const voice = allVoices.find(v => v.id === voiceId);
      if (voice) {
        return voice.uri;
      }
    } catch (error) {
      this.logError('VOICE_MAPPING_ERROR', '获取音色列表失败，使用默认映射', { error });
    }

    // 回退到预设语音映射
    return this.SUPPORTED_VOICES[voiceId as keyof typeof this.SUPPORTED_VOICES] ||
           this.SUPPORTED_VOICES['zh-CN-XiaoxiaoNeural'];
  }

  /**
   * 计算费用
   */
  private static calculateCost(characters: number): number {
    // 每字符 $0.0002
    return characters * 0.0002;
  }

  /**
   * 创建音频 URL（用于播放）
   */
  static createAudioUrl(audioData: ArrayBuffer): string {
    const blob = new Blob([audioData], { type: 'audio/mpeg' });
    return URL.createObjectURL(blob);
  }

  /**
   * 下载音频文件
   */
  static downloadAudio(audioData: ArrayBuffer, filename: string = 'speech.mp3'): void {
    const blob = new Blob([audioData], { type: 'audio/mpeg' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }

  /**
   * 获取支持的语音列表
   */
  static async getSupportedVoices(userId?: string): Promise<Array<{ id: string; name: string; preview: string; isCustom?: boolean; isCloned?: boolean; gender?: string }>> {
    const cacheKey = userId || 'system';

    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      const cachedVoices = this.voiceCache.get(cacheKey);
      if (cachedVoices) {
        this.logInfo('Using cached voices', { userId, count: cachedVoices.length });
        return cachedVoices;
      }
    }

    try {
      this.logInfo('Fetching voices from database', { userId });

      // 优先使用新的音色管理系统
      const voices = await VoiceService.getAllVoices(userId);
      const formattedVoices = VoiceService.formatVoicesForUI(voices);

      // 如果数据库中没有音色，返回默认音色
      if (formattedVoices.length === 0) {
        this.logInfo('No voices found in database, returning default voices');
        const defaultVoices = this.getDefaultVoices();

        // 更新缓存
        this.voiceCache.set(cacheKey, defaultVoices);
        this.cacheTimestamp.set(cacheKey, Date.now());

        return defaultVoices;
      }

      // 更新缓存
      this.voiceCache.set(cacheKey, formattedVoices);
      this.cacheTimestamp.set(cacheKey, Date.now());

      this.logInfo('Voices fetched successfully', { userId, count: formattedVoices.length });
      return formattedVoices;
    } catch (error) {
      this.logError('GET_VOICES_ERROR', '从音色管理系统获取音色失败，尝试旧系统', { error });

      try {
        // 回退到旧的音色列表服务
        const allVoices = await VoiceListService.getAllVoices();
        const formattedVoices = allVoices.map(voice => ({
          id: voice.id,
          name: voice.name,
          preview: voice.preview,
          isCustom: voice.isCustom,
        }));

        // 如果旧系统也没有音色，返回默认音色
        if (formattedVoices.length === 0) {
          const defaultVoices = this.getDefaultVoices();
          this.voiceCache.set(cacheKey, defaultVoices);
          this.cacheTimestamp.set(cacheKey, Date.now());
          return defaultVoices;
        }

        // 更新缓存
        this.voiceCache.set(cacheKey, formattedVoices);
        this.cacheTimestamp.set(cacheKey, Date.now());

        return formattedVoices;
      } catch (fallbackError) {
        this.logError('GET_VOICES_FALLBACK_ERROR', '获取音色列表完全失败，返回默认列表', { fallbackError });

        // 返回默认音色列表
        const defaultVoices = this.getDefaultVoices();

        // 更新缓存
        this.voiceCache.set(cacheKey, defaultVoices);
        this.cacheTimestamp.set(cacheKey, Date.now());

        return defaultVoices;
      }
    }
  }

  /**
   * 获取默认音色列表
   */
  private static getDefaultVoices(): Array<{ id: string; name: string; preview: string; isCustom?: boolean; isCloned?: boolean; gender?: string }> {
    return [
      { id: 'zh-CN-XiaoxiaoNeural', name: '温柔知性女声', preview: '你好，我是小晓', isCustom: false, gender: 'female' },
      { id: 'zh-CN-YunxiNeural', name: '沉稳磁性男声', preview: '你好，我是云希', isCustom: false, gender: 'male' },
      { id: 'zh-CN-XiaoyiNeural', name: '活泼青春女声', preview: '你好，我是小艺', isCustom: false, gender: 'female' },
      { id: 'zh-CN-YunjianNeural', name: '专业播音男声', preview: '你好，我是云健', isCustom: false, gender: 'male' },
    ];
  }

  /**
   * 检查缓存是否有效
   */
  private static isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamp.get(cacheKey);
    return timestamp ? (Date.now() - timestamp < this.CACHE_DURATION) : false;
  }

  /**
   * 清除音色缓存
   */
  static clearVoiceCache(userId?: string): void {
    if (userId) {
      this.voiceCache.delete(userId);
      this.cacheTimestamp.delete(userId);
    } else {
      this.voiceCache.clear();
      this.cacheTimestamp.clear();
    }
    this.logInfo('Voice cache cleared', { userId });
  }

  /**
   * 获取错误消息
   */
  private static getErrorMessage(status: number): string {
    const errorMessages: Record<number, string> = {
      400: '请求参数错误',
      401: 'API 密钥无效',
      403: '访问被拒绝',
      404: '接口不存在',
      429: '请求频率过高，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时',
    };

    return errorMessages[status] || '未知错误';
  }

  /**
   * 记录信息日志
   */
  private static logInfo(message: string, data?: any): void {
    console.log(`[TTS Service] ${message}`, data);
  }

  /**
   * 记录错误日志
   */
  private static logError(code: string, message: string, data?: any): void {
    console.error(`[TTS Service] ${code}: ${message}`, data);

    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }

  /**
   * 获取服务状态
   */
  static async getServiceStatus(): Promise<{ available: boolean; latency?: number }> {
    const startTime = Date.now();

    try {
      const response = await fetch(this.API_URL, {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
        },
      });

      const latency = Date.now() - startTime;

      return {
        available: response.ok,
        latency,
      };
    } catch (error) {
      return {
        available: false,
      };
    }
  }
}
